@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Facebook Section Static Gallery Strips */
.gallery-strip {
  position: absolute;
  top: 0;
  height: 100%;
  overflow: hidden;
  will-change: auto;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

.gallery-content {
  will-change: auto;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  height: 100%;
}

.gallery-image {
  position: relative;
  overflow: hidden;
  will-change: auto;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
}

@layer utilities {
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .shimmer {
    position: relative;
    overflow: hidden;
    background-color: #f0f0f0;
  }

  .shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 0.8s infinite;
  }

  @keyframes shimmer-wave {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  .animate-shimmer {
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite linear;
  }

  .animate-shimmer-wave {
    animation: shimmer-wave 2s infinite linear;
  }

  /* Enhanced Shiny Silver Shimmer Effect for Gallery */
  @keyframes silver-shimmer {
    0% {
      transform: translateX(-100%) skewX(-12deg);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
      opacity: 0;
    }
  }

  @keyframes silver-shimmer-wave {
    0% {
      transform: translateX(-100%) skewX(-15deg);
      opacity: 0;
    }
    25% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
    75% {
      opacity: 0.8;
    }
    100% {
      transform: translateX(200%) skewX(-15deg);
      opacity: 0;
    }
  }

  .animate-silver-shimmer {
    animation: silver-shimmer 2s infinite ease-in-out;
  }

  .animate-silver-shimmer-wave {
    animation: silver-shimmer-wave 2.5s infinite ease-in-out;
  }

  /* Performance optimizations for gallery images */
  .gallery-image {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .gallery-image:hover {
    will-change: transform;
  }
}

/* Cute mobile-friendly styles */
.cute-font {
  font-family: 'Nunito', sans-serif;
}

.sharp-container {
  border-radius: 0.375rem; /* 6px - sharp but not harsh */
}

.mini-container {
  padding: 0.75rem; /* 12px */
  margin: 0.5rem 0; /* 8px vertical */
}

/* Neon Red Glow Animation for Donate Buttons */
@keyframes neon-glow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(239, 68, 68, 0.6),
      0 0 40px rgba(239, 68, 68, 0.4),
      0 0 60px rgba(239, 68, 68, 0.2),
      inset 0 0 20px rgba(239, 68, 68, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(239, 68, 68, 0.8),
      0 0 60px rgba(239, 68, 68, 0.6),
      0 0 90px rgba(239, 68, 68, 0.4),
      inset 0 0 30px rgba(239, 68, 68, 0.2);
  }
}

@keyframes neon-pulse {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.9),
      0 0 10px rgba(255, 255, 255, 0.7),
      0 0 15px rgba(239, 68, 68, 0.3);
  }
  50% {
    text-shadow:
      0 0 8px rgba(255, 255, 255, 1),
      0 0 15px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(239, 68, 68, 0.4);
  }
}

.neon-red-glow {
  animation: neon-glow 2s ease-in-out infinite alternate;
}

.neon-text-glow {
  animation: neon-pulse 2s ease-in-out infinite alternate;
}

/* Simple loading animations only */

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    letter-spacing: -0.025em;
  }

  p {
    letter-spacing: -0.01em;
  }
}

@keyframes shimmer {
  0% {
    background-position: 0% 0%, 10% 10%, 20% 20%, 30% 30%, 40% 40%, 50% 50%, 60% 60%, 70% 70%, 80% 80%, 90% 90%, 100% 100%, 0% 0%, 0% 0%;
  }
  100% {
    background-position: 100% 100%, 90% 90%, 80% 80%, 70% 70%, 60% 60%, 50% 50%, 40% 40%, 30% 30%, 20% 20%, 10% 10%, 0% 0%, 100% 100%, 100% 100%;
  }
}

@layer utilities {
  .glass {
    @apply bg-black/30 backdrop-blur-md border border-white/10;
  }

  .glass-card {
    @apply bg-zinc-900/50 backdrop-blur-md border border-white/10;
  }

  .glass-dark {
    @apply bg-zinc-950/80 backdrop-blur-xl border border-white/10;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .glass-homepage {
    @apply bg-black/85 backdrop-blur-2xl border-b border-white/20;
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }

  /* Hide scrollbars for horizontal scrolling */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

:root {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
}

body {
  @apply bg-black text-white;
  /* Prevent white flash/edge blinking and optimize performance */
  background-color: #000000 !important;
  overflow-x: hidden !important;
  max-width: 100vw;
  /* Performance optimizations */
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent white edges and layout shifts */
html {
  background-color: #000000 !important;
  overflow-x: hidden !important;
  max-width: 100vw;
  /* Smooth scrolling performance */
  scroll-behavior: smooth;
}

/* Smooth header transitions */
header {
  will-change: background-color, backdrop-filter, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
}


/* Shimmer loading effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.shimmer-dark {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

.shimmer-dark::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

/* Global performance optimizations */
* {
  /* Prevent layout thrashing */
  box-sizing: border-box;
}

/* Optimize images for faster loading */
img {
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Prevent layout shifts */
  max-width: 100%;
  height: auto;
}

/* Optimize animations for 60fps */
@media (prefers-reduced-motion: no-preference) {
  * {
    animation-duration: 0.3s !important;
    animation-timing-function: ease-out !important;
  }
}

/* Force hardware acceleration for smooth scrolling */
section, main, div[class*="container"] {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Mobile-specific fixes to prevent animation conflicts and improve performance */
@media (max-width: 768px) {
  /* Disable conflicting animations on mobile */
  .animate-heartbeat {
    animation: none !important;
  }

  /* Ensure hero section doesn't interfere with header */
  .hero-section {
    isolation: isolate;
    contain: layout style;
  }

  /* Prevent any transform conflicts */
  .hero-section * {
    will-change: auto;
  }

  /* Stabilize loading dots only */
  .animate-bounce {
    animation-duration: 1s !important;
  }

  /* Prevent horizontal overflow on homepage */
  body {
    overflow-x: hidden !important;
    position: relative;
  }

  /* Stabilize header on homepage */
  header {
    position: fixed !important;
    width: 100vw !important;
    max-width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    contain: layout style;
  }

  /* Ensure container doesn't overflow */
  .container {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  /* Reduce complex animations on mobile for better performance */
  .animate-pulse {
    animation-duration: 1.5s !important;
  }

  .animate-ping {
    animation: none !important;
  }

  /* Optimize scroll performance on mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Reduce motion for better performance */
  .motion-reduce {
    animation: none !important;
    transition: none !important;
  }

  /* Force GPU acceleration for smooth scrolling */
  section, main, article {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Optimize image rendering on mobile */
  img {
    image-rendering: optimizeSpeed;
    image-rendering: -webkit-optimize-contrast;
  }
}

.bg-gray-50 {
  @apply glass;
}

.bg-gray-100 {
  @apply glass-dark;
}

.text-gray-700 {
  @apply text-gray-300;
}

.text-gray-600 {
  @apply text-gray-400;
}

.text-gray-900 {
  @apply text-white;
}

.shadow-md {
  @apply shadow-black/20;
}

.shadow-lg {
  @apply shadow-black/30;
}

.border-gray-300 {
  @apply border-zinc-700;
}

.hover\:bg-gray-100:hover {
  @apply hover:bg-zinc-800;
}

.bg-primary-50 {
  @apply bg-zinc-900;
}

.bg-primary-100 {
  @apply bg-zinc-800;
}

/* Gallery Custom Styles with Enhanced Silver Shimmer */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Yet Another React Lightbox Custom Styles */
.yarl__container {
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

.yarl__slide {
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3)) !important;
}

.yarl__button {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.yarl__button:hover {
  background: rgba(0, 0, 0, 0.8) !important;
  transform: scale(1.05) !important;
}

/* Optimize for 3xl screens */
@media (min-width: 1600px) {
  .grid-cols-3xl-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }
}

/* PayPal Button Glow Animation */
@keyframes paypal-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(59, 130, 246, 0.3), 0 0 60px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 60px rgba(59, 130, 246, 0.5), 0 0 90px rgba(59, 130, 246, 0.3);
  }
}

.paypal-glow {
  animation: paypal-glow 2s ease-in-out infinite;
}

/* Enhanced Donation Button Effects */
.donation-button {
  position: relative;
  background: linear-gradient(135deg, var(--btn-from), var(--btn-via), var(--btn-to));
  border: 1px solid var(--btn-border);
  box-shadow:
    0 4px 15px var(--btn-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.donation-button:hover {
  transform: translateY(-4px);
  box-shadow:
    0 8px 25px var(--btn-shadow),
    0 0 20px var(--btn-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.donation-button:active {
  transform: translateY(-1px);
}

/* Silver reflection animation */
@keyframes silver-reflection {
  0% {
    transform: translateX(-100%) skewX(-12deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
    opacity: 0;
  }
}

.silver-reflection {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: silver-reflection 2s ease-in-out infinite;
  pointer-events: none;
}

/* Emoji fallback system */
.emoji-fallback {
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

/* Fallback for systems that don't support flag emojis */
@supports not (font-variation-settings: normal) {
  .emoji-fallback[data-emoji="🇺🇸"]::before {
    content: attr(data-fallback);
    font-size: 1.2em;
    font-weight: bold;
    color: #059669;
  }

  .emoji-fallback[data-emoji="🇬🇧"]::before {
    content: attr(data-fallback);
    font-size: 1.2em;
    font-weight: bold;
    color: #7c3aed;
  }

  .emoji-fallback[data-emoji="🇪🇺"]::before {
    content: attr(data-fallback);
    font-size: 1.2em;
    font-weight: bold;
    color: #059669;
  }

  .emoji-fallback[data-emoji="🇺🇸"],
  .emoji-fallback[data-emoji="🇬🇧"],
  .emoji-fallback[data-emoji="🇪🇺"] {
    font-size: 0;
  }
}

/* Currency symbol styling */
.currency-symbol {
  font-size: 1.2em !important;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  display: inline-block;
  transform: scale(1.1);
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .donation-button {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }

  .currency-symbol {
    font-size: 1rem !important;
  }
}

/* Smooth scrolling improvements */
html {
  scroll-behavior: smooth;
}

/* Prevent scrolling glitches */
body {
  -webkit-overflow-scrolling: touch;
}

/* Lightning bolt effects for transfer service cards */
@keyframes lightning-flash {
  0%, 100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.transfer-card:hover .lightning-bolt {
  animation: lightning-flash 0.3s ease-in-out;
}

/* Sharp silver lightning effect */
@keyframes silver-lightning {
  0% {
    transform: translateX(-100%) skewX(-12deg) scaleX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
    transform: translateX(0%) skewX(-12deg) scaleX(1);
  }
  100% {
    transform: translateX(100%) skewX(-12deg) scaleX(0);
    opacity: 0;
  }
}

.silver-lightning {
  animation: silver-lightning 0.4s ease-out;
}

/* Transfer service cards attention-grabbing glow */
@keyframes transfer-glow {
  0%, 100% {
    box-shadow:
      0 4px 15px var(--glow-color-40),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow:
      0 6px 20px var(--glow-color-60),
      0 0 25px var(--glow-color-30),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }
}

/* Apply glow to transfer service cards */
a[href*="taptapsend"] {
  --glow-color-30: rgba(239, 68, 68, 0.3);
  --glow-color-40: rgba(239, 68, 68, 0.4);
  --glow-color-60: rgba(239, 68, 68, 0.6);
  animation: transfer-glow 3s ease-in-out infinite;
}

a[href*="payangel"] {
  --glow-color-30: rgba(59, 130, 246, 0.3);
  --glow-color-40: rgba(59, 130, 246, 0.4);
  --glow-color-60: rgba(59, 130, 246, 0.6);
  animation: transfer-glow 3s ease-in-out infinite;
  animation-delay: 0.5s;
}

a[href*="sendwave"] {
  --glow-color-30: rgba(34, 197, 94, 0.3);
  --glow-color-40: rgba(34, 197, 94, 0.4);
  --glow-color-60: rgba(34, 197, 94, 0.6);
  animation: transfer-glow 3s ease-in-out infinite;
  animation-delay: 1s;
}

a[href*="worldremit"] {
  --glow-color-30: rgba(147, 51, 234, 0.3);
  --glow-color-40: rgba(147, 51, 234, 0.4);
  --glow-color-60: rgba(147, 51, 234, 0.6);
  animation: transfer-glow 3s ease-in-out infinite;
  animation-delay: 1.5s;
}

/* Extra shiny silver shimmer for gallery images */
@keyframes silver-shine {
  0% {
    transform: translateX(-100%) skewX(-20deg);
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
    filter: brightness(1.5);
  }
  80% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(200%) skewX(-20deg);
    opacity: 0;
  }
}

.silver-shimmer-enhanced {
  position: relative;
  overflow: hidden;
}

.silver-shimmer-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: silver-shine 2.5s infinite ease-in-out;
  z-index: 1;
}

.text-primary-800 {
  @apply text-white;
}

.text-primary-600 {
  @apply text-gray-300;
}